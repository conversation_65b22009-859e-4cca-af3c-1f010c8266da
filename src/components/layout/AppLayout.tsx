import React from "react";
import { Link, useLocation } from "react-router-dom";
import {
  BarChart,
  FileSpreadsheet,
  Building2,
  DollarSign,
  FileText,
  PieChart,
  TrendingUp,
  Home,
  Package,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

interface AppLayoutProps {
  children: React.ReactNode;
}

const menuItems = [
  {
    title: "Overview",
    url: "/",
    icon: Home,
  },
  {
    title: "Summary Dashboard",
    url: "/dashboard/summary",
    icon: BarChart,
  },
  {
    title: "Contract Awards",
    url: "/dashboard/contract-awards",
    icon: FileSpreadsheet,
  },
  {
    title: "Value Put in Place",
    url: "/dashboard/value-put-in-place",
    icon: Building2,
  },
  {
    title: "Federal Aid",
    url: "/dashboard/federal-aid",
    icon: DollarSign,
  },
  {
    title: "State Legislative",
    url: "/dashboard/state-legislative-initiatives",
    icon: FileText,
  },
  {
    title: "State DOT Budgets",
    url: "/dashboard/state-dot-budgets",
    icon: PieChart,
  },
  {
    title: "Material Prices",
    url: "/dashboard/material-prices",
    icon: TrendingUp,
  },
];

const utilityItems = [
  {
    title: "Import Dashboard",
    url: "/import",
    icon: Package,
  },
];

export function AppLayout({ children }: AppLayoutProps) {
  const location = useLocation();

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <Sidebar>
          <SidebarHeader className="p-4">
            <h2 className="text-lg font-semibold">ARTBA Economics</h2>
            <p className="text-sm text-muted-foreground">Analytics Dashboard</p>
          </SidebarHeader>
          
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Dashboards</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {menuItems.map((item) => (
                    <SidebarMenuItem key={item.url}>
                      <SidebarMenuButton
                        asChild
                        isActive={location.pathname === item.url}
                      >
                        <Link to={item.url}>
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarGroup>
              <SidebarGroupLabel>Utilities</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {utilityItems.map((item) => (
                    <SidebarMenuItem key={item.url}>
                      <SidebarMenuButton
                        asChild
                        isActive={location.pathname === item.url}
                      >
                        <Link to={item.url}>
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          <SidebarFooter className="p-4 border-t">
            <p className="text-xs text-muted-foreground">
              © 2024 ARTBA. All rights reserved.
            </p>
          </SidebarFooter>
        </Sidebar>

        <main className="flex-1 overflow-auto">
          <div className="sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background px-4">
            <SidebarTrigger />
          </div>
          <div className="p-6">{children}</div>
        </main>
      </div>
    </SidebarProvider>
  );
}