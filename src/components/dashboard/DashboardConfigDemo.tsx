import React, { useState } from 'react';
import { DashboardMetadataService } from '../../services/dashboardMetadata.service';
import { DashboardConfigurationService } from '../../services/dashboardConfiguration.service';
import { DashboardMigrationUtil } from '../../utils/dashboardMigration.util';

/**
 * Demo component to showcase the centralized dashboard configuration system
 */
export const DashboardConfigDemo: React.FC = () => {
  const [selectedDashboardId, setSelectedDashboardId] = useState<string>('');
  const [showValidation, setShowValidation] = useState(false);

  const allDashboards = DashboardMetadataService.getAllDashboards();
  const dashboardSummary = DashboardConfigurationService.getDashboardSummary();
  const migrationSummary = DashboardMigrationUtil.getMigrationSummary();
  const validation = DashboardMigrationUtil.validateMetadata();

  const selectedDashboard = selectedDashboardId 
    ? DashboardMetadataService.getDashboardById(selectedDashboardId)
    : null;

  const selectedConfig = selectedDashboardId
    ? DashboardConfigurationService.getDashboardConfig(selectedDashboardId)
    : null;

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Centralized Dashboard Configuration Demo</h1>
      
      {/* System Overview */}
      <section style={{ marginBottom: '30px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
        <h2>System Overview</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div>
            <strong>Total Dashboards:</strong> {allDashboards.length}
          </div>
          <div>
            <strong>Configured Dashboards:</strong> {migrationSummary.configuredDashboards}
          </div>
          <div>
            <strong>Total Widgets:</strong> {allDashboards.reduce((sum, d) => sum + d.Charts.length, 0)}
          </div>
          <div>
            <strong>Metadata Valid:</strong> {validation.isValid ? '✅ Yes' : '❌ No'}
          </div>
        </div>
      </section>

      {/* Dashboard Selector */}
      <section style={{ marginBottom: '30px' }}>
        <h2>Dashboard Explorer</h2>
        <select 
          value={selectedDashboardId} 
          onChange={(e) => setSelectedDashboardId(e.target.value)}
          style={{ padding: '8px', marginBottom: '15px', width: '100%', maxWidth: '500px' }}
        >
          <option value="">Select a dashboard...</option>
          {allDashboards.map(dashboard => (
            <option key={dashboard.Dashboard_ID} value={dashboard.Dashboard_ID}>
              {DashboardMetadataService.cleanDashboardTitle(dashboard.Dashboard_Title)} 
              ({dashboard.Charts.length} widgets)
            </option>
          ))}
        </select>

        {selectedDashboard && (
          <div style={{ padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
            <h3>{DashboardMetadataService.cleanDashboardTitle(selectedDashboard.Dashboard_Title)}</h3>
            <p><strong>ID:</strong> {selectedDashboard.Dashboard_ID}</p>
            <p><strong>Source:</strong> {selectedDashboard.Dashboard_Source}</p>
            <p><strong>Widgets:</strong> {selectedDashboard.Charts.length}</p>
            
            {selectedConfig && (
              <div style={{ marginTop: '15px' }}>
                <h4>Centralized Configuration:</h4>
                <p><strong>Title:</strong> {selectedConfig.title}</p>
                <p><strong>Description:</strong> {selectedConfig.description}</p>
                <p><strong>Path:</strong> {selectedConfig.path}</p>
                <p><strong>Has Custom Layout:</strong> {DashboardConfigurationService.hasCustomLayout(selectedDashboardId) ? '✅ Yes' : '❌ No'}</p>
              </div>
            )}

            <details style={{ marginTop: '15px' }}>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Widget Details ({selectedDashboard.Charts.length})</summary>
              <div style={{ marginTop: '10px', maxHeight: '300px', overflowY: 'auto' }}>
                {selectedDashboard.Charts.map((chart, index) => (
                  <div key={chart.Chart_ID} style={{ 
                    padding: '8px', 
                    margin: '5px 0', 
                    backgroundColor: 'white', 
                    borderRadius: '3px',
                    fontSize: '14px'
                  }}>
                    <strong>{index + 1}. {chart.Chart_Title || 'Untitled Widget'}</strong>
                    <br />
                    <span style={{ color: '#666' }}>
                      ID: {chart.Chart_ID} | Type: {chart.Chart_Type} | Subtype: {chart.Chart_Subtype}
                    </span>
                  </div>
                ))}
              </div>
            </details>
          </div>
        )}
      </section>

      {/* Dashboard Summary */}
      <section style={{ marginBottom: '30px' }}>
        <h2>Configured Dashboards</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
          {dashboardSummary.map(dashboard => (
            <div key={dashboard.id} style={{ 
              padding: '15px', 
              border: '1px solid #ddd', 
              borderRadius: '5px',
              backgroundColor: '#f9f9f9'
            }}>
              <h3 style={{ margin: '0 0 10px 0' }}>{dashboard.icon} {dashboard.title}</h3>
              <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>{dashboard.description}</p>
              <p style={{ margin: '5px 0', fontSize: '12px' }}>
                <strong>Path:</strong> {dashboard.path}<br />
                <strong>Widgets:</strong> {dashboard.widgetCount}
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Validation Results */}
      <section style={{ marginBottom: '30px' }}>
        <h2>
          Metadata Validation 
          <button 
            onClick={() => setShowValidation(!showValidation)}
            style={{ marginLeft: '10px', padding: '5px 10px', fontSize: '12px' }}
          >
            {showValidation ? 'Hide' : 'Show'} Details
          </button>
        </h2>
        
        <div style={{ padding: '10px', backgroundColor: validation.isValid ? '#d4edda' : '#f8d7da', borderRadius: '5px' }}>
          <strong>Status:</strong> {validation.isValid ? '✅ Valid' : '❌ Invalid'}
        </div>

        {showValidation && (
          <div style={{ marginTop: '15px' }}>
            {validation.errors.length > 0 && (
              <div style={{ marginBottom: '15px' }}>
                <h4 style={{ color: '#dc3545' }}>Errors:</h4>
                <ul>
                  {validation.errors.map((error, index) => (
                    <li key={index} style={{ color: '#dc3545' }}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {validation.warnings.length > 0 && (
              <div>
                <h4 style={{ color: '#ffc107' }}>Warnings:</h4>
                <ul>
                  {validation.warnings.map((warning, index) => (
                    <li key={index} style={{ color: '#ffc107' }}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </section>

      {/* Migration Summary */}
      <section>
        <h2>Migration Status</h2>
        <div style={{ padding: '15px', backgroundColor: '#e7f3ff', borderRadius: '5px' }}>
          <p><strong>Progress:</strong> {migrationSummary.configuredDashboards} of {migrationSummary.totalDashboards} dashboards configured</p>
          
          {migrationSummary.unconfiguredDashboards.length > 0 && (
            <details>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                Unconfigured Dashboards ({migrationSummary.unconfiguredDashboards.length})
              </summary>
              <ul style={{ marginTop: '10px' }}>
                {migrationSummary.unconfiguredDashboards.map((dashboard, index) => (
                  <li key={index}>{dashboard}</li>
                ))}
              </ul>
            </details>
          )}

          <details style={{ marginTop: '15px' }}>
            <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Recommendations</summary>
            <ul style={{ marginTop: '10px' }}>
              {migrationSummary.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </details>
        </div>
      </section>
    </div>
  );
};
