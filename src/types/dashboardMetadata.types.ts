// Types for dashboard_metadata.json structure

export interface DashboardMetadata {
  Dashboard_ID: string;
  Dashboard_Title: string;
  Dashboard_Source: string;
  Dashboard_Type: string;
  Charts: ChartMetadata[];
}

export interface ChartMetadata {
  Chart_ID: string;
  Chart_Title: string;
  Chart_Type: string;
  Chart_Subtype: string;
  Chart_DataSource_Title: string;
  Chart_DataSource_ID: string;
  Chart_DataSource_Database: string;
  Chart_DataSource_Fullname: string;
  Chart_Filters: string;
}

// Mapping types for converting metadata to dashboard config
export interface DashboardMetadataMapping {
  dashboardId: string;
  title: string;
  description?: string;
  path: string;
  icon?: string;
  widgets: WidgetMetadataMapping[];
}

export interface WidgetMetadataMapping {
  id: string;
  title: string;
  type: 'chart' | 'kpi' | 'table' | 'pivot' | 'text' | 'filter';
  chartType?: string;
  chartSubtype?: string;
  dataSource: string;
  filters?: string;
  position?: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
}

// Configuration for dashboard layout and positioning
export interface DashboardLayoutConfig {
  [dashboardId: string]: {
    title: string;
    description: string;
    path: string;
    icon?: string;
    widgetLayout: {
      [widgetId: string]: {
        position: {
          x: number;
          y: number;
          w: number;
          h: number;
        };
        customTitle?: string;
        visible?: boolean;
        tab?: string;
      };
    };
  };
}
