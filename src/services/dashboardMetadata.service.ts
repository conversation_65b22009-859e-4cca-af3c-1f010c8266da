import type { 
  DashboardMetadata, 
  ChartMetadata, 
  DashboardMetadataMapping,
  WidgetMetadataMapping,
  DashboardLayoutConfig
} from '../types/dashboardMetadata.types';
import type { DashboardConfig, WidgetConfig, WidgetType } from '../types/dashboard.types';
import dashboardMetadataJson from '../../dashboard_metadata.json';

export class DashboardMetadataService {
  private static dashboardMetadata: DashboardMetadata[] = dashboardMetadataJson as DashboardMetadata[];
  
  /**
   * Get all available dashboards from metadata
   */
  static getAllDashboards(): DashboardMetadata[] {
    return this.dashboardMetadata;
  }

  /**
   * Get dashboard by ID
   */
  static getDashboardById(dashboardId: string): DashboardMetadata | undefined {
    return this.dashboardMetadata.find(dashboard => dashboard.Dashboard_ID === dashboardId);
  }

  /**
   * Get dashboard by title (partial match)
   */
  static getDashboardByTitle(title: string): DashboardMetadata | undefined {
    return this.dashboardMetadata.find(dashboard => 
      dashboard.Dashboard_Title.toLowerCase().includes(title.toLowerCase())
    );
  }

  /**
   * Get all widgets for a dashboard
   */
  static getWidgetsForDashboard(dashboardId: string): ChartMetadata[] {
    const dashboard = this.getDashboardById(dashboardId);
    return dashboard?.Charts || [];
  }

  /**
   * Get widget by ID
   */
  static getWidgetById(widgetId: string): { widget: ChartMetadata; dashboard: DashboardMetadata } | undefined {
    for (const dashboard of this.dashboardMetadata) {
      const widget = dashboard.Charts.find(chart => chart.Chart_ID === widgetId);
      if (widget) {
        return { widget, dashboard };
      }
    }
    return undefined;
  }

  /**
   * Map chart type from metadata to widget type
   */
  static mapChartTypeToWidgetType(chartType: string, chartSubtype: string): WidgetType {
    if (chartType === 'indicator' || chartSubtype.includes('indicator')) {
      return 'kpi';
    }
    if (chartType.startsWith('chart/') || chartType === 'chart') {
      return 'chart';
    }
    if (chartType === 'pivot2' || chartType === 'pivot') {
      return 'pivot';
    }
    if (chartType === 'richtexteditor') {
      return 'text';
    }
    if (chartType === 'map') {
      return 'chart';
    }
    if (chartType === 'BloX') {
      return 'chart'; // Treat BloX as chart for now
    }
    return 'chart'; // Default fallback
  }

  /**
   * Convert dashboard metadata to dashboard config format
   */
  static convertToDashboardConfig(
    dashboardId: string, 
    layoutConfig?: DashboardLayoutConfig[string]
  ): DashboardConfig | undefined {
    const dashboard = this.getDashboardById(dashboardId);
    if (!dashboard) return undefined;

    const title = layoutConfig?.title || this.cleanDashboardTitle(dashboard.Dashboard_Title);
    const description = layoutConfig?.description || `Dashboard for ${title}`;
    const path = layoutConfig?.path || `/dashboard/${this.slugify(title)}`;

    const widgets: WidgetConfig[] = dashboard.Charts.map((chart, index) => {
      const widgetType = this.mapChartTypeToWidgetType(chart.Chart_Type, chart.Chart_Subtype);
      const layoutInfo = layoutConfig?.widgetLayout[chart.Chart_ID];
      
      return {
        id: chart.Chart_ID,
        type: widgetType,
        title: layoutInfo?.customTitle || chart.Chart_Title || `Widget ${index + 1}`,
        position: layoutInfo?.position || this.getDefaultPosition(index, widgetType),
        config: {
          chartType: chart.Chart_Type,
          chartSubtype: chart.Chart_Subtype,
          dataSource: chart.Chart_DataSource_Title,
          filters: chart.Chart_Filters !== 'N/A' ? chart.Chart_Filters : undefined,
        },
        visibleOn: layoutInfo?.tab ? [layoutInfo.tab] : undefined,
      };
    }).filter(widget => {
      const layoutInfo = layoutConfig?.widgetLayout[widget.id];
      return layoutInfo?.visible !== false;
    });

    return {
      id: dashboardId,
      title,
      description,
      path,
      icon: layoutConfig?.icon,
      widgets,
    };
  }

  /**
   * Clean dashboard title by removing numbering and SDK suffix
   */
  static cleanDashboardTitle(title: string): string {
    return title
      .replace(/^\d+\.\s*/, '') // Remove leading number and dot
      .replace(/_SDK$/, '') // Remove _SDK suffix
      .trim();
  }

  /**
   * Generate default position for widget based on index and type
   */
  private static getDefaultPosition(index: number, type: WidgetType): { x: number; y: number; w: number; h: number } {
    const row = Math.floor(index / 3);
    const col = index % 3;
    
    // Default sizes based on widget type
    const defaultSizes = {
      kpi: { w: 3, h: 2 },
      chart: { w: 6, h: 4 },
      table: { w: 12, h: 4 },
      pivot: { w: 12, h: 4 },
      text: { w: 12, h: 2 },
      filter: { w: 3, h: 1 },
    };

    const size = defaultSizes[type] || defaultSizes.chart;
    
    return {
      x: col * 4,
      y: row * (size.h + 1),
      w: size.w,
      h: size.h,
    };
  }

  /**
   * Create URL-friendly slug from title
   */
  private static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  /**
   * Get dashboard summary information
   */
  static getDashboardSummary(): Array<{
    id: string;
    title: string;
    widgetCount: number;
    dataSource: string;
  }> {
    return this.dashboardMetadata.map(dashboard => ({
      id: dashboard.Dashboard_ID,
      title: this.cleanDashboardTitle(dashboard.Dashboard_Title),
      widgetCount: dashboard.Charts.length,
      dataSource: dashboard.Charts[0]?.Chart_DataSource_Title || 'Unknown',
    }));
  }
}
