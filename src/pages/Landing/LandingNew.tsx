import React from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart,
  FileSpreadsheet,
  Building2,
  DollarSign,
  FileText,
  PieChart,
  TrendingUp,
  ArrowRight,
  Package,
  BookOpen,
  TrendingDown,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface Dashboard {
  id: string;
  title: string;
  description: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const dashboards: Dashboard[] = [
  {
    id: "summary",
    title: "Summary Dashboard",
    description:
      "Executive summary of key transportation metrics including contract awards, federal obligations, and material prices.",
    path: "/dashboard/summary",
    icon: Bar<PERSON>hart,
    color: "bg-blue-500",
  },
  {
    id: "contract-awards",
    title: "Contract Awards",
    description:
      "Track state and local contract awards with TTM and YTD metrics, geographic distribution, and trending analysis.",
    path: "/dashboard/contract-awards",
    icon: FileSpreadsheet,
    color: "bg-cyan-500",
  },
  {
    id: "value-put-in-place",
    title: "Value Put in Place",
    description:
      "Monitor construction value put in place across highways, bridges, and other infrastructure projects.",
    path: "/dashboard/value-put-in-place",
    icon: Building2,
    color: "bg-purple-500",
  },
  {
    id: "federal-aid",
    title: "Federal-Aid Obligations",
    description:
      "Track federal highway aid obligations by state and program, with historical trends and distributions.",
    path: "/dashboard/federal-aid",
    icon: DollarSign,
    color: "bg-green-500",
  },
  {
    id: "state-legislative",
    title: "State Legislative Initiatives",
    description:
      "Monitor state transportation funding initiatives, ballot measures, and legislative actions.",
    path: "/dashboard/state-legislative-initiatives",
    icon: FileText,
    color: "bg-red-500",
  },
  {
    id: "state-dot-budgets",
    title: "State DOT Budgets 2025",
    description:
      "Analyze state department of transportation budgets, funding sources, and year-over-year changes.",
    path: "/dashboard/state-dot-budgets",
    icon: PieChart,
    color: "bg-orange-500",
  },
  {
    id: "material-prices",
    title: "Material Prices",
    description:
      "Track construction material price indices for asphalt, concrete, steel, and aggregates with trend analysis.",
    path: "/dashboard/material-prices",
    icon: TrendingUp,
    color: "bg-teal-500",
  },
];

interface StatCardProps {
  title: string;
  value: string | number;
  change: number;
  icon: React.ComponentType<{ className?: string }>;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, icon: Icon }) => {
  const isPositive = change > 0;
  const ChangeIcon = isPositive ? TrendingUp : TrendingDown;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className={cn("flex items-center gap-1 text-xs", 
          isPositive ? "text-green-600" : "text-red-600"
        )}>
          <ChangeIcon className="h-3 w-3" />
          <span>{Math.abs(change)}%</span>
          <span className="text-muted-foreground">from last month</span>
        </div>
      </CardContent>
    </Card>
  );
};

export const Landing: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="New Cases"
          value="104"
          change={14.88}
          icon={BarChart}
        />
        <StatCard
          title="New Tasks"
          value="34"
          change={-5.67}
          icon={FileSpreadsheet}
        />
        <StatCard
          title="Active Projects"
          value="7"
          change={16.7}
          icon={Building2}
        />
        <StatCard
          title="Completion Rate"
          value="92%"
          change={3.23}
          icon={TrendingUp}
        />
      </div>

      {/* Hero Section */}
      <Card className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
        <CardHeader className="relative z-10 pb-8 pt-12 text-center">
          <CardTitle className="text-4xl font-bold tracking-tight lg:text-5xl">
            ARTBA Economics Dashboard
          </CardTitle>
          <CardDescription className="mx-auto mt-4 max-w-2xl text-lg">
            Transportation construction industry analytics and economic indicators
          </CardDescription>
        </CardHeader>
        <CardContent className="relative z-10 pb-12">
          <div className="flex flex-wrap justify-center gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">7</div>
              <div className="text-sm text-muted-foreground">Dashboards</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">15+</div>
              <div className="text-sm text-muted-foreground">Data Sources</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">24/7</div>
              <div className="text-sm text-muted-foreground">Real-time</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dashboards Section */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight">Explore Dashboards</h2>
          <p className="mx-auto mt-2 max-w-2xl text-muted-foreground">
            Dive into comprehensive analytics across different aspects of transportation construction
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {dashboards.map((dashboard) => {
            const Icon = dashboard.icon;
            return (
              <Card
                key={dashboard.id}
                className="group cursor-pointer transition-all hover:shadow-lg"
                onClick={() => navigate(dashboard.path)}
              >
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className={cn("rounded-lg p-2", dashboard.color)}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-lg group-hover:text-primary transition-colors">
                      {dashboard.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription>{dashboard.description}</CardDescription>
                  <div className="mt-4 flex items-center text-sm text-primary">
                    <span className="group-hover:underline">View Dashboard</span>
                    <ArrowRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* CTA Section */}
      <Card className="relative overflow-hidden bg-primary text-primary-foreground">
        <div className="absolute right-0 top-0 h-40 w-40 rounded-full bg-white/10 blur-3xl" />
        <CardHeader className="relative z-10 text-center">
          <CardTitle className="text-3xl">Ready to Get Started?</CardTitle>
          <CardDescription className="mt-2 text-primary-foreground/90">
            Import your own dashboards or explore our comprehensive documentation
          </CardDescription>
        </CardHeader>
        <CardContent className="relative z-10 flex flex-wrap justify-center gap-4 pb-8">
          <Button
            size="lg"
            variant="secondary"
            onClick={() => navigate("/import")}
            className="gap-2"
          >
            <Package className="h-4 w-4" />
            Import Dashboard
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="gap-2 border-primary-foreground/20 bg-transparent text-primary-foreground hover:bg-primary-foreground/10"
          >
            <BookOpen className="h-4 w-4" />
            View Documentation
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};