/**
 * Unified Contract Awards Dashboard component
 * Supports both Sisense widgets and custom charts via prop
 */

import React from 'react';
import { DashboardLayout } from '../../../components/layout/DashboardLayout';
import { SisenseWidget } from '../../../components/widgets/SisenseWidget/SisenseWidget';
import { CustomBarChart } from '../../../components/charts/CustomBarChart';
import { CustomLineChart } from '../../../components/charts/CustomLineChart';
import { CustomPieChart } from '../../../components/charts/CustomPieChart';
import { KPIWidget } from '../../../components/widgets/KPIWidget/KPIWidget';
import { DashboardConfigurationService } from '../../../services/dashboardConfiguration.service';
import { ErrorBoundary } from '../../../components/common/ErrorBoundary';
import { useSisenseStyleOverride } from '../../../hooks/useSisenseStyleOverride';

interface ContractAwardsDashboardProps {
  useCustomCharts?: boolean;
}

export const ContractAwardsDashboard: React.FC<ContractAwardsDashboardProps> = ({
  useCustomCharts = false
}) => {
  // Get dashboard configuration from centralized service
  const dashboardConfig = DashboardConfigurationService.getDashboardConfig('68654950099a11833ea60935');

  if (!dashboardConfig) {
    return (
      <ErrorBoundary context="contract-awards-dashboard">
        <div>Dashboard configuration not found</div>
      </ErrorBoundary>
    );
  }

  const dashboardId = dashboardConfig.id;

  // Apply persistent style overrides for Sisense widgets
  useSisenseStyleOverride();

  // Mock data for custom charts
  const mockData = {
    totalAwards: {
      value: 485.7,
      trend: 15.3,
    },
    stateAwards: [
      { state: 'CA', awards: 52.3 },
      { state: 'TX', awards: 48.7 },
      { state: 'FL', awards: 41.2 },
      { state: 'NY', awards: 38.9 },
      { state: 'IL', awards: 35.4 },
    ],
    monthlyTrend: [
      { month: 'Jan', awards: 425.2 },
      { month: 'Feb', awards: 438.6 },
      { month: 'Mar', awards: 452.1 },
      { month: 'Apr', awards: 468.3 },
      { month: 'May', awards: 475.9 },
      { month: 'Jun', awards: 485.7 },
    ],
    contractTypes: [
      { name: 'Design-Build', value: 35, color: '#0088FE' },
      { name: 'Design-Bid-Build', value: 45, color: '#00C49F' },
      { name: 'CM/GC', value: 15, color: '#FFBB28' },
      { name: 'P3', value: 5, color: '#FF8042' },
    ],
  };

  const renderWidget = (widgetId: string, position: any, title?: string) => {
    if (useCustomCharts) {
      // Map widget IDs to custom chart components using actual metadata IDs
      switch (widgetId) {
        case '68654950099a11833ea60936': // Monthly Value KPI
          return (
            <KPIWidget
              key={widgetId}
              title="Monthly Value"
              value={`$${mockData.totalAwards.value}B`}
              trend={mockData.totalAwards.trend}
              position={position}
            />
          );

        case '68654950099a11833ea6093b': // YTD Value Chart
          return (
            <CustomLineChart
              key={widgetId}
              data={mockData.monthlyTrend}
              xKey="month"
              yKeys={['awards']}
              title="YTD Value"
              position={position}
              colors={['#8884d8']}
            />
          );
        case '68654950099a11833ea6093c': // YTD Value by Mode Pie Chart
          return (
            <CustomPieChart
              key={widgetId}
              data={mockData.contractTypes}
              title="YTD Value by Mode"
              position={position}
            />
          );
        default:
          return (
            <SisenseWidget
              key={widgetId}
              widgetId={widgetId}
              dashboardId={dashboardId}
              title={title}
              position={position}
            />
          );
      }
    }

    // Default: render Sisense widget
    return (
      <SisenseWidget
        key={widgetId}
        widgetId={widgetId}
        dashboardId={dashboardId}
        title={title}
        position={position}
      />
    );
  };

  return (
    <ErrorBoundary context="contract-awards-dashboard">
      <DashboardLayout
        title={dashboardConfig.title}
        description={dashboardConfig.description}
      >
        {dashboardConfig.widgets.map((widget) =>
          renderWidget(widget.id, widget.position, widget.title)
        )}
      </DashboardLayout>
    </ErrorBoundary>
  );
};

// Export variation for backward compatibility
export const ContractAwardsDashboardWithCustomCharts: React.FC = () => (
  <ContractAwardsDashboard useCustomCharts={true} />
);