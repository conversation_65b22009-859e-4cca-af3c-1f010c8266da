import type { DashboardConfig } from '../../../types/dashboard.types';

export const contractAwardsDashboardConfig: DashboardConfig = {
  id: '68654950099a11833ea60935',
  title: 'Contract Awards',
  description: 'State & Local Government Contract Awards with TTM and YTD metrics, geographic distribution, and trending analysis',
  path: '/dashboard/contract-awards',
  widgets: [
    // Top KPI Row - Always Visible for All Tabs
    {
      id: '68654950099a11833ea60936',
      type: 'kpi',
      title: 'Monthly Value',
      position: { x: 0, y: 0, w: 3, h: 2 },
      config: {
        value: 10900000000,
        format: 'currency',
        suffix: 'B',
        prefix: '$',
        subtitle: 'Number of Projects: 4,136',
        color: '#00cee6',
      },
    },
    {
      id: '68654950099a11833ea60937',
      type: 'kpi',
      title: 'YTD Value',
      position: { x: 3, y: 0, w: 3, h: 2 },
      config: {
        value: 54800000000,
        format: 'currency',
        suffix: 'B',
        prefix: '$',
        subtitle: 'Number of Projects: 15,191',
        color: '#9b9bd7',
      },
    },
    {
      id: '68654950099a11833ea60941',
      type: 'kpi',
      title: 'TTM Value',
      position: { x: 6, y: 0, w: 3, h: 2 },
      config: {
        value: 124200000000,
        format: 'currency',
        suffix: 'B',
        prefix: '$',
        subtitle: 'Number of Projects: 38,711',
        color: '#6EDA55',
      },
    },

    // ========== MONTH TAB WIDGETS ==========
    {
      id: '68654950099a11833ea6094a',
      type: 'chart',
      title: 'Monthly Value',
      position: { x: 0, y: 4, w: 4, h: 4 },
      visibleOn: ['MONTH'],
      config: {},
    },
    {
      id: '68654950099a11833ea6094e',
      type: 'chart',
      title: 'Monthly Value by Mode',
      position: { x: 4, y: 4, w: 4, h: 4 },
      visibleOn: ['MONTH'],
      config: {},
    },

    {
      id: '68654950099a11833ea60939',
      type: 'pivot',
      title: 'Monthly Value by Mode and Year',
      position: { x: 0, y: 8, w: 6, h: 6 },
      visibleOn: ['MONTH'],
      config: {},
    },
    {
      id: '68654950099a11833ea6093e',
      type: 'pivot',
      title: 'Monthly Value by State and Year',
      position: { x: 6, y: 8, w: 6, h: 6 },
      visibleOn: ['MONTH'],
      config: {},
    },
    {
      id: '68654950099a11833ea6093d',
      type: 'chart',
      title: 'Monthly Number of Contract Awards',
      position: { x: 0, y: 14, w: 6, h: 4 },
      visibleOn: ['MONTH'],
      config: {},
    },
    {
      id: '68654950099a11833ea6094b',
      type: 'pivot',
      title: 'Monthly % Change by Mode',
      position: { x: 6, y: 14, w: 6, h: 4 },
      visibleOn: ['MONTH'],
      config: {},
    },

    // ========== YTD TAB WIDGETS ==========
    {
      id: '68654950099a11833ea6093b',
      type: 'chart',
      title: 'YTD Value',
      position: { x: 0, y: 4, w: 4, h: 4 },
      visibleOn: ['YTD'],
      config: {},
    },
    {
      id: '68654950099a11833ea6093c',
      type: 'chart',
      title: 'YTD Value by Mode',
      position: { x: 4, y: 4, w: 4, h: 4 },
      visibleOn: ['YTD'],
      config: {},
    },
    {
      id: '68654950099a11833ea60940',
      type: 'chart',
      title: 'YTD Value by State',
      position: { x: 8, y: 4, w: 4, h: 4 },
      visibleOn: ['YTD'],
      config: {},
    },
    {
      id: '68654950099a11833ea60942',
      type: 'pivot',
      title: 'YTD Value by State and Year',
      position: { x: 0, y: 8, w: 6, h: 6 },
      visibleOn: ['YTD'],
      config: {},
    },
    {
      id: '68654950099a11833ea6093f',
      type: 'pivot',
      title: 'YTD Value by Mode and Year',
      position: { x: 6, y: 8, w: 6, h: 6 },
      visibleOn: ['YTD'],
      config: {},
    },
    {
      id: '68654950099a11833ea60943',
      type: 'chart',
      title: 'YTD Number of Contract Awards',
      position: { x: 0, y: 14, w: 6, h: 4 },
      visibleOn: ['YTD'],
      config: {},
    },
    {
      id: '68654950099a11833ea6094c',
      type: 'pivot',
      title: 'YTD % Change by Mode',
      position: { x: 6, y: 14, w: 6, h: 4 },
      visibleOn: ['YTD'],
      config: {},
    },

    // ========== TTM TAB WIDGETS ==========
    {
      id: '68654950099a11833ea60945',
      type: 'chart',
      title: 'TTM Value',
      position: { x: 0, y: 4, w: 4, h: 4 },
      visibleOn: ['TTM'],
      config: {},
    },
    {
      id: '68654950099a11833ea6094f',
      type: 'chart',
      title: 'TTM Value by Mode',
      position: { x: 4, y: 4, w: 4, h: 4 },
      visibleOn: ['TTM'],
      config: {},
    },
    {
      id: '68654950099a11833ea60948',
      type: 'chart',
      title: 'TTM Value of Contract Awards by State',
      position: { x: 8, y: 4, w: 4, h: 4 },
      visibleOn: ['TTM'],
      config: {},
    },
    {
      id: '68654950099a11833ea60944',
      type: 'pivot',
      title: 'Trailing 12-Month Totals by State',
      position: { x: 0, y: 8, w: 6, h: 6 },
      visibleOn: ['TTM'],
      config: {},
    },
    {
      id: '68654950099a11833ea60947',
      type: 'pivot',
      title: 'TTM Value by Mode and Month',
      position: { x: 6, y: 8, w: 6, h: 6 },
      visibleOn: ['TTM'],
      config: {},
    },
    {
      id: '68654950099a11833ea60946',
      type: 'chart',
      title: 'TTM Number of Contract Awards',
      position: { x: 0, y: 14, w: 6, h: 4 },
      visibleOn: ['TTM'],
      config: {},
    },
    {
      id: '68654950099a11833ea6094d',
      type: 'pivot',
      title: 'TTM % Change by Mode',
      position: { x: 6, y: 14, w: 6, h: 4 },
      visibleOn: ['TTM'],
      config: {},
    },

    // ========== ANNUAL TAB WIDGETS ==========
    {
      id: '68654950099a11833ea60956',
      type: 'chart',
      title: 'Annual Value',
      position: { x: 0, y: 4, w: 4, h: 4 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
    {
      id: '68654950099a11833ea60955',
      type: 'chart',
      title: '2024 Annual Value by Mode',
      position: { x: 4, y: 4, w: 4, h: 4 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
    {
      id: '68654950099a11833ea60959',
      type: 'chart',
      title: '2024 Value by State',
      position: { x: 8, y: 4, w: 4, h: 4 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
    {
      id: '68654950099a11833ea60958',
      type: 'pivot',
      title: 'Annual Value by State',
      position: { x: 0, y: 8, w: 6, h: 6 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
    {
      id: '68654950099a11833ea60953',
      type: 'pivot',
      title: 'Annual Value by Mode',
      position: { x: 6, y: 8, w: 6, h: 6 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
    {
      id: '68654950099a11833ea60957',
      type: 'chart',
      title: 'Annual Number of Contract Awards',
      position: { x: 0, y: 14, w: 6, h: 4 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
    {
      id: '68654950099a11833ea60954',
      type: 'pivot',
      title: 'Annual % Change by Mode',
      position: { x: 6, y: 14, w: 6, h: 4 },
      visibleOn: ['ANNUAL'],
      config: {},
    },
  ],
};