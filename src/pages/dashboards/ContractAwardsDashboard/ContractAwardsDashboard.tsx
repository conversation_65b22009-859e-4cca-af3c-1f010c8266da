import React, { useState, useEffect } from "react";
import { PageLayout } from "../../../components/layout/PageLayout";
import { DashboardHeader } from "../../../components/layout/DashboardHeader";
import { DashboardActions } from "../../../components/layout/DashboardActions";
import { TabNavigation } from "../../../components/layout/TabNavigation";
import { UniversalDashboardFilters } from "../../../components/filters/UniversalDashboardFilters";
import type { FilterValue } from "../../../components/filters/UniversalDashboardFilters";
import { setupDashboardFilters } from "../../../utils/dashboardFilters";

import { LoadingSpinner } from "../../../components/layout/LoadingSpinner";
import {
  ErrorBoundary,
  ErrorDisplay,
} from "../../../components/layout/ErrorBoundary";
import { theme } from "../../../config/theme.config";
import { createWidget } from "../../../utils/widgetFactory";
import { contractAwardsDashboardConfig } from "./contractAwardsDashboard.config";
import "./ContractAwardsDashboard.css";

export const ContractAwardsDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("MONTH");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const [windowWidth, setWindowWidth] = useState<number>(window.innerWidth);

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Get filter configuration for Contract Awards dashboard
  const filterSetup = setupDashboardFilters.contractAwards();
  const {
    id: dashboardId,
    widgets,
    title,
    description,
  } = contractAwardsDashboardConfig;

  // Simulate loading dashboard data
  useEffect(() => {
    const loadDashboard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Simulate API call delay
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // Simulate potential error (uncomment to test error state)
        // if (Math.random() > 0.8) {
        //   throw new Error('Failed to load dashboard data');
        // }

        setIsLoading(false);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred",
        );
        setIsLoading(false);
      }
    };

    loadDashboard();
  }, []);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    // Export functionality to be implemented
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    // Trigger reload
    window.location.reload();
  };

  // Define tabs for navigation
  const tabs = [
    { id: "MONTH", label: "Monthly", icon: "📅" },
    { id: "YTD", label: "Year to Date", icon: "📊" },
    { id: "TTM", label: "Trailing 12M", icon: "📈" },
    { id: "ANNUAL", label: "Annual", icon: "🗓️" },
  ];

  // Separate widgets by type
  const kpiWidgets = widgets.filter((widget) => !widget.visibleOn);
  const tabSpecificWidgets = widgets.filter(
    (widget) => widget.visibleOn && widget.visibleOn.includes(activeTab),
  );

  // Show loading state
  if (isLoading) {
    return (
      <PageLayout title="Contract Awards Dashboard" data-oid="or4703:">
        <div
          style={{
            minHeight: "100vh",
            background: theme.colors.backgroundGradient,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          data-oid="l2-l9h3"
        >
          <LoadingSpinner
            size="lg"
            message="Loading Contract Awards Dashboard..."
            data-oid="hk42wai"
          />
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout title="Contract Awards Dashboard" data-oid="zafx71y">
        <div
          style={{
            minHeight: "100vh",
            background: theme.colors.backgroundGradient,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: theme.spacing.xl,
          }}
          data-oid="rowfeae"
        >
          <ErrorDisplay
            title="Dashboard Loading Error"
            message={error}
            onRetry={handleRetry}
            icon="📊"
            data-oid="d_9ose7"
          />
        </div>
      </PageLayout>
    );
  }

  return (
    <ErrorBoundary data-oid="jsl0iak">
      <PageLayout title="Contract Awards Dashboard" data-oid="y077wp-">
        <div
          style={{
            minHeight: "100vh",
            background: theme.colors.backgroundGradient,
            padding: theme.spacing.xl,
          }}
          data-oid="qkr3_ag"
        >
          {/* Dashboard Header */}
          <DashboardHeader
            title={title}
            description={description}
            actions={
              <DashboardActions onExport={handleExport} data-oid="gkv:oiz" />
            }
            data-oid="lsflz-o"
          />

          {/* Dashboard Filters - Moved to top for better UX */}
          <div
            style={{
              width: "100%",
              maxWidth: "1400px",
              margin: "0 auto",
              marginBottom: theme.spacing.xxl,
            }}
          >
            <UniversalDashboardFilters
              dashboardType={filterSetup.dashboardType}
              onFiltersChange={setDashboardFilters}
              variant="horizontal"
            />
          </div>

          {/* KPI Section - Always visible widgets */}
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              gap: theme.spacing.xl,
              marginBottom: theme.spacing.xxl,
              width: "100%",
              maxWidth: "1400px",
              margin: "0 auto",
              flexWrap: "wrap",
              justifyContent: "center",
              padding: `0 ${theme.spacing.lg}px`,
            }}
            data-oid="m3tq-kj"
          >
            {kpiWidgets.map((widget) => (
              <div
                key={widget.id}
                style={{
                  flex: "1 1 300px",
                  minWidth: "300px",
                  maxWidth: "400px",
                }}
                data-oid="cl14px4"
              >
                {createWidget(widget, dashboardId, activeTab, dashboardFilters)}
              </div>
            ))}
          </div>

          {/* Tab Navigation */}
          <div
            style={{
              width: "100%",
              maxWidth: "1400px",
              margin: "0 auto",
              marginBottom: theme.spacing.xl,
              padding: `0 ${theme.spacing.lg}px`,
            }}
          >
            <TabNavigation
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              variant="pills"
              size="md"
              data-oid="3z1q4ii"
            />
          </div>

          {/* Tab-specific widgets - Enhanced Two-Column Layout */}
          <div
            style={{
              width: "100%",
              maxWidth: "1400px",
              margin: "0 auto",
              padding: `0 ${theme.spacing.lg}px`,
              marginBottom: theme.spacing.xxl,
            }}
            data-oid="1.t6t_w"
          >
            {/* Responsive Grid Container */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: windowWidth > 1200 ? "repeat(2, 1fr)" : "1fr",
                gap: theme.spacing.xl,
                gridAutoRows: "minmax(400px, auto)",
                width: "100%",
              }}
              className="dashboard-responsive-grid"
            >
              {tabSpecificWidgets.map((widget, index) => {
                // Enhanced widget positioning for two-column layout
                const enhancedPosition = {
                  ...widget.position,
                  // For tables, ensure full width within their grid cell
                  w: widget.type === 'pivot' || widget.type === 'table' ? 12 : widget.position.w,
                };

                return (
                  <div
                    key={widget.id}
                    style={{
                      // Enhanced glass morphism styling
                      background: theme.colors.surface,
                      backdropFilter: `blur(${theme.blur.lg})`,
                      WebkitBackdropFilter: `blur(${theme.blur.lg})`,
                      borderRadius: theme.borderRadius.lg,
                      boxShadow: theme.shadows.glass,
                      border: "none", // Remove borders as per user preference
                      padding: theme.spacing.lg,
                      display: "flex",
                      flexDirection: "column",
                      overflow: "hidden",
                      transition: `all ${theme.transitions.normal}`,
                      minHeight: widget.type === 'pivot' || widget.type === 'table' ? "500px" : "400px",
                      // Hover effect for better interactivity
                      "&:hover": {
                        transform: "translateY(-2px)",
                        boxShadow: theme.shadows.elevated,
                      },
                    }}
                    className={`widget-container widget-${widget.type}`}
                  >
                    {createWidget(widget, dashboardId, activeTab, dashboardFilters, enhancedPosition)}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </PageLayout>
    </ErrorBoundary>
  );
};
