/* Contract Awards Dashboard Enhanced Styling */

/* Responsive Grid Layout */
.dashboard-responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
  gap: 24px;
  grid-auto-rows: minmax(400px, auto);
}

/* Responsive breakpoints */
@media (max-width: 1200px) {
  .dashboard-responsive-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-responsive-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    grid-auto-rows: minmax(350px, auto);
  }
}

/* Widget Container Enhancements */
.widget-container {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Enhanced glass morphism effect */
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow:
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.widget-container:hover {
  transform: translateY(-2px);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.3);
}

/* Table-specific styling */
.widget-table,
.widget-pivot {
  min-height: 500px;
}



/* Remove default widget borders and frames */
.widget-container [data-testid="widget-wrapper"],
.widget-container .widget-wrapper,
.widget-container .sisense-widget-container {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* Hide widget titles when custom titles are used */
.widget-container .sisense-widget-title {
  display: none !important;
}

/* Enhanced chart styling */
.widget-container .sisense-widget .highcharts-container {
  border-radius: 8px;
  overflow: hidden;
}

/* Improved typography for widget content */
.widget-container .sisense-widget {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #1a202c;
}

/* Loading states */
.widget-container .loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #718096;
}

/* Error states */
.widget-container .error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #e53e3e;
  text-align: center;
  padding: 20px;
}

/* Responsive adjustments for mobile */
@media (max-width: 480px) {
  .dashboard-responsive-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    grid-auto-rows: minmax(300px, auto);
  }
  
  .widget-container {
    padding: 16px;
    min-height: 300px;
  }
  

}

/* Accessibility improvements */
.widget-container:focus-within {
  outline: 2px solid #4299e1;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .dashboard-responsive-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .widget-container {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .widget-container {
    border: 2px solid #000;
    background: rgba(255, 255, 255, 0.9);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .widget-container {
    transition: none;
  }
  
  .widget-container:hover {
    transform: none;
  }
}
