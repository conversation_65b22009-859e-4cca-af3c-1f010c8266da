import { DashboardMetadataService } from '../services/dashboardMetadata.service';
import { DashboardConfigurationService } from '../services/dashboardConfiguration.service';
import type { DashboardConfig } from '../types/dashboard.types';

/**
 * Utility functions to help with migrating from hardcoded configs to centralized configuration
 */
export class DashboardMigrationUtil {
  
  /**
   * Compare a hardcoded dashboard config with the centralized version
   */
  static compareDashboardConfigs(
    hardcodedConfig: DashboardConfig,
    dashboardId: string
  ): {
    matches: boolean;
    differences: string[];
    suggestions: string[];
  } {
    const centralizedConfig = DashboardConfigurationService.getDashboardConfig(dashboardId);
    const differences: string[] = [];
    const suggestions: string[] = [];

    if (!centralizedConfig) {
      return {
        matches: false,
        differences: [`Dashboard ID ${dashboardId} not found in centralized configuration`],
        suggestions: ['Add dashboard configuration to DashboardConfigurationService'],
      };
    }

    // Compare basic properties
    if (hardcodedConfig.title !== centralizedConfig.title) {
      differences.push(`Title mismatch: "${hardcodedConfig.title}" vs "${centralizedConfig.title}"`);
    }

    if (hardcodedConfig.description !== centralizedConfig.description) {
      differences.push(`Description mismatch: "${hardcodedConfig.description}" vs "${centralizedConfig.description}"`);
    }

    if (hardcodedConfig.path !== centralizedConfig.path) {
      differences.push(`Path mismatch: "${hardcodedConfig.path}" vs "${centralizedConfig.path}"`);
    }

    // Compare widget counts
    if (hardcodedConfig.widgets.length !== centralizedConfig.widgets.length) {
      differences.push(`Widget count mismatch: ${hardcodedConfig.widgets.length} vs ${centralizedConfig.widgets.length}`);
    }

    // Compare widget IDs
    const hardcodedWidgetIds = new Set(hardcodedConfig.widgets.map(w => w.id));
    const centralizedWidgetIds = new Set(centralizedConfig.widgets.map(w => w.id));
    
    const missingInCentralized = [...hardcodedWidgetIds].filter(id => !centralizedWidgetIds.has(id));
    const extraInCentralized = [...centralizedWidgetIds].filter(id => !hardcodedWidgetIds.has(id));

    if (missingInCentralized.length > 0) {
      differences.push(`Widgets in hardcoded but missing in centralized: ${missingInCentralized.join(', ')}`);
      suggestions.push('Add missing widgets to centralized configuration layout');
    }

    if (extraInCentralized.length > 0) {
      differences.push(`Extra widgets in centralized: ${extraInCentralized.join(', ')}`);
      suggestions.push('Consider if these widgets should be included or hidden');
    }

    return {
      matches: differences.length === 0,
      differences,
      suggestions,
    };
  }

  /**
   * Generate migration code for a dashboard
   */
  static generateMigrationCode(dashboardId: string): string {
    const config = DashboardConfigurationService.getDashboardConfig(dashboardId);
    if (!config) {
      return `// Dashboard ${dashboardId} not found in centralized configuration`;
    }

    return `
// Migrated dashboard using centralized configuration
import React from 'react';
import { CentralizedDashboard } from '../../../components/dashboard/CentralizedDashboard';

const DASHBOARD_ID = '${dashboardId}';

export const ${this.toPascalCase(config.title)}Dashboard: React.FC = () => {
  return (
    <CentralizedDashboard
      dashboardId={DASHBOARD_ID}
    />
  );
};

// With custom widget renderer example:
export const ${this.toPascalCase(config.title)}DashboardWithCustomCharts: React.FC = () => {
  const customWidgetRenderer = (widgetId: string, position: any, title?: string) => {
    switch (widgetId) {
      ${config.widgets.slice(0, 3).map(widget => `
      case '${widget.id}': // ${widget.title}
        return (
          <CustomWidget
            key={widgetId}
            title="${widget.title}"
            position={position}
          />
        );`).join('')}
      default:
        return null; // Use default Sisense widget
    }
  };

  return (
    <CentralizedDashboard
      dashboardId={DASHBOARD_ID}
      customWidgetRenderer={customWidgetRenderer}
    />
  );
};
`;
  }

  /**
   * Get migration summary for all dashboards
   */
  static getMigrationSummary(): {
    totalDashboards: number;
    configuredDashboards: number;
    unconfiguredDashboards: string[];
    recommendations: string[];
  } {
    const allDashboards = DashboardMetadataService.getAllDashboards();
    const configuredCount = DashboardConfigurationService.getAllDashboardConfigs().length;
    
    const unconfigured = allDashboards
      .filter(dashboard => !DashboardConfigurationService.hasCustomLayout(dashboard.Dashboard_ID))
      .map(dashboard => `${dashboard.Dashboard_Title} (${dashboard.Dashboard_ID})`);

    const recommendations = [
      'Use CentralizedDashboard component for new dashboards',
      'Migrate existing dashboards gradually using the comparison utility',
      'Add layout configurations for unconfigured dashboards',
      'Remove hardcoded dashboard config files after migration',
      'Update routing to use centralized dashboard paths',
    ];

    return {
      totalDashboards: allDashboards.length,
      configuredDashboards: configuredCount,
      unconfiguredDashboards: unconfigured,
      recommendations,
    };
  }

  /**
   * Convert string to PascalCase
   */
  private static toPascalCase(str: string): string {
    return str
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * Validate dashboard metadata integrity
   */
  static validateMetadata(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      const dashboards = DashboardMetadataService.getAllDashboards();
      
      if (dashboards.length === 0) {
        errors.push('No dashboards found in metadata');
        return { isValid: false, errors, warnings };
      }

      // Check for duplicate dashboard IDs
      const dashboardIds = dashboards.map(d => d.Dashboard_ID);
      const duplicateIds = dashboardIds.filter((id, index) => dashboardIds.indexOf(id) !== index);
      if (duplicateIds.length > 0) {
        errors.push(`Duplicate dashboard IDs found: ${duplicateIds.join(', ')}`);
      }

      // Check for duplicate widget IDs across all dashboards
      const allWidgetIds: string[] = [];
      dashboards.forEach(dashboard => {
        dashboard.Charts.forEach(chart => {
          if (allWidgetIds.includes(chart.Chart_ID)) {
            errors.push(`Duplicate widget ID found: ${chart.Chart_ID}`);
          }
          allWidgetIds.push(chart.Chart_ID);
        });
      });

      // Check for missing titles
      dashboards.forEach(dashboard => {
        if (!dashboard.Dashboard_Title) {
          warnings.push(`Dashboard ${dashboard.Dashboard_ID} has no title`);
        }
        
        dashboard.Charts.forEach(chart => {
          if (!chart.Chart_Title || chart.Chart_Title.trim() === '') {
            warnings.push(`Widget ${chart.Chart_ID} in dashboard ${dashboard.Dashboard_ID} has no title`);
          }
        });
      });

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    } catch (error) {
      errors.push(`Failed to validate metadata: ${error}`);
      return { isValid: false, errors, warnings };
    }
  }
}
