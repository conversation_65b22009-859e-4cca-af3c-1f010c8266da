{"name": "econ", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@sisense/sdk-cli": "latest", "@sisense/sdk-data": "latest", "@sisense/sdk-query-client": "^2.5.0", "@sisense/sdk-ui": "latest", "@types/d3": "^7.4.3", "@types/geojson": "^7946.0.16", "@types/react-simple-maps": "^3.0.6", "@types/react-tooltip": "^3.11.0", "@types/topojson-client": "^3.1.5", "@types/topojson-specification": "^1.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1", "react-simple-maps": "^3.0.0", "react-tooltip": "^5.29.1", "recharts": "^2.15.4", "tailwind-merge": "^3.3.1", "topojson-client": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "overrides": {"react-custom-scrollbars": {"react": "$react", "react-dom": "$react-dom"}, "leaflet": "1.9.4", "proj4leaflet": "1.0.2"}}