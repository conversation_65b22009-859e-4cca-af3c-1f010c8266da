#!/usr/bin/env python3
import json
import os
import csv

def extract_widgets_from_dashboard(file_path):
    """Extract widget information from a Sisense dashboard file"""
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    widgets = []
    dashboard_id = data.get('oid', '')
    dashboard_title = data.get('title', '')
    
    for widget in data.get('widgets', []):
        widget_info = {
            'dashboard_id': dashboard_id,
            'dashboard_title': dashboard_title,
            'widget_id': widget.get('oid', ''),
            'widget_title': widget.get('title', ''),
            'widget_type': widget.get('type', ''),
            'widget_subtype': widget.get('subtype', ''),
        }
        widgets.append(widget_info)
    
    return widgets

def main():
    dashboards_dir = '/Users/<USER>/projects/Wrok/econ/dashboards'
    
    # Dictionary to store widget type mappings
    widget_mappings = {}
    
    # Process all .dash files
    for filename in os.listdir(dashboards_dir):
        if filename.endswith('.dash'):
            file_path = os.path.join(dashboards_dir, filename)
            try:
                widgets = extract_widgets_from_dashboard(file_path)
                for widget in widgets:
                    widget_mappings[widget['widget_id']] = {
                        'type': widget['widget_type'],
                        'subtype': widget['widget_subtype']
                    }
                print(f"Processed {filename}: found {len(widgets)} widgets")
            except Exception as e:
                print(f"Error processing {filename}: {e}")
    
    # Read existing CSV and update types
    input_csv = '/Users/<USER>/projects/Wrok/econ/dashboard_metadata.csv'
    output_csv = '/Users/<USER>/projects/Wrok/econ/dashboard_metadata_updated.csv'
    
    # Read all rows first
    rows = []
    with open(input_csv, 'r') as infile:
        reader = csv.reader(infile)
        header = next(reader)
        # Add widget_subtype to header if not present
        if 'widget_subtype' not in header:
            header.append('widget_subtype')
        
        # Convert remaining rows to dictionaries
        for row in reader:
            row_dict = {}
            for i, value in enumerate(row):
                if i < len(header):
                    row_dict[header[i]] = value
            rows.append(row_dict)
    
    # Write updated CSV
    with open(output_csv, 'w', newline='') as outfile:
        writer = csv.DictWriter(outfile, fieldnames=header)
        writer.writeheader()
        
        for row in rows:
            widget_id = row.get('widget_id', '')
            if widget_id in widget_mappings:
                # Update the widget type with actual Sisense type
                row['widget_type'] = widget_mappings[widget_id]['type']
                row['widget_subtype'] = widget_mappings[widget_id]['subtype']
            else:
                row['widget_subtype'] = ''
            
            writer.writerow(row)
    
    print(f"\nUpdated CSV saved to: {output_csv}")
    print(f"Total widgets mapped: {len(widget_mappings)}")
    
    # Show some examples of the mapping
    print("\nSample mappings:")
    for i, (widget_id, mapping) in enumerate(widget_mappings.items()):
        if i < 5:
            print(f"  {widget_id}: {mapping['type']} / {mapping['subtype']}")

if __name__ == "__main__":
    main()