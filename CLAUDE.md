# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start Vite development server with hot module replacement
- `npm run build` - Type check with TypeScript then build for production with Vite
- `npm run lint` - Run ESLint on all files (configured with TypeScript and React hooks)
- `npm run preview` - Preview production build locally

### shadcn/ui Components
- `npx shadcn@latest add <component>` - Add a new shadcn/ui component
- `npx shadcn@latest diff` - Check for component updates

### Environment Setup
1. Copy `.env.example` to `.env`
2. Add Sisense instance URL and API token:
   ```
   VITE_INSTANCE_URL=https://your-instance.sisense.com
   VITE_TOKEN=your-api-token-here
   ```

## Architecture Overview

### Core Technologies
- **React 18** with TypeScript for UI components
- **Vite** as build tool and dev server
- **shadcn/ui** - Component library built on Radix UI and Tailwind CSS
- **Tailwind CSS v4** - Utility-first CSS framework
- **Sisense SDK** (@sisense/sdk-ui, sdk-data, sdk-query-client) for dashboard integration
- **React Router** for navigation between dashboards
- **D3.js** and **Recharts** for custom visualizations
- **React Simple Maps** for geographical visualizations
- **Lucide React** - Icon library used throughout the UI

### Application Structure

#### Entry Point Flow
1. `src/main.tsx` - Sets up React root with StrictMode, SisenseContextProvider, and FilterProvider
2. `src/App.tsx` - Defines routing structure with BrowserRouter, wraps with ThemeProvider
3. Individual dashboard pages load widgets using Sisense SDK's WidgetById component

#### Key Directories
- `src/pages/dashboards/` - Dashboard implementations (7 transportation industry dashboards)
- `src/components/widgets/` - Widget wrappers and custom implementations
- `src/config/` - Configuration for Sisense connection, filters, themes, and data models
- `src/contexts/` - React contexts for theme and filter state management
- `src/services/` - Service layer including logger and Sisense-specific utilities
- `src/utils/` - Utility functions for dashboard filters, widget factory, and data model inspection

### Dashboard Architecture

Each dashboard follows this pattern:
1. Dashboard component loads configuration from a `.config.ts` file
2. Configuration contains dashboard ID and widget metadata (widget IDs, positions, types)
3. Widgets are loaded using `WidgetById` from Sisense SDK or custom chart components
4. Filter context provides global filtering capabilities across widgets

The application supports two modes:
- **Sisense mode**: When environment variables are configured, loads real widgets from Sisense
- **Development mode**: Without credentials, shows placeholder widgets with widget IDs

### Important Patterns

#### Widget Loading
- Uses centralized widget configuration service at `src/services/sisense/widgetConfigService.ts`
- Widgets are identified by dashboard ID + widget ID combination
- Error boundaries handle widget loading failures gracefully

#### State Management
- FilterContext manages global dashboard filters
- ThemeContext provides theming capabilities
- No Redux - uses React Context API for state management

#### Error Handling
- Global error handlers in main.tsx catch CRS (Coordinate Reference System) errors
- Individual widget error boundaries prevent cascade failures
- Comprehensive logging through custom logger service

### Vite Configuration
- Includes optimizeDeps for leaflet, proj4, and proj4leaflet to handle CRS issues
- Uses React plugin for JSX transformation

### TypeScript Configuration
- Strict mode enabled with comprehensive type checking
- Module resolution set to "bundler" for Vite compatibility
- Separate configs for app code and node scripts
- Path aliases configured: `@/*` maps to `./src/*`

## UI Component System

### shadcn/ui Setup
- Components are in `src/components/ui/`
- Configuration in `components.json`
- Utility function `cn()` in `src/lib/utils.ts` for className merging
- Using default theme with CSS variables for theming

### Layout Structure
- `AppLayout` - Main application layout with sidebar navigation
- `DashboardLayout` - Layout wrapper for dashboard pages
- Responsive sidebar using shadcn/ui Sidebar component
- All dashboards wrapped in consistent layout

### Styling Approach
- Tailwind CSS for utility classes
- CSS variables for theme colors (defined in `src/index.css`)
- Component variants handled by class-variance-authority
- Dark mode support via CSS variables