#!/usr/bin/env python3
import json
import os
import csv
from typing import Dict, List, <PERSON><PERSON>

def extract_widgets_from_dashboard(file_path: str) -> List[Dict]:
    """Extract widget information from a Sisense dashboard file"""
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    widgets = []
    dashboard_id = data.get('oid', '')
    dashboard_title = data.get('title', '')
    
    for widget in data.get('widgets', []):
        widget_info = {
            'dashboard_id': dashboard_id,
            'dashboard_title': dashboard_title,
            'widget_id': widget.get('oid', ''),
            'widget_title': widget.get('title', ''),
            'widget_type': widget.get('type', ''),
            'widget_subtype': widget.get('subtype', ''),
        }
        widgets.append(widget_info)
    
    return widgets

def main():
    dashboards_dir = '/Users/<USER>/projects/Wrok/econ/dashboards'
    
    # Dictionary to store widget type mappings
    widget_mappings = {}
    
    # Process all .dash files
    for filename in os.listdir(dashboards_dir):
        if filename.endswith('.dash'):
            file_path = os.path.join(dashboards_dir, filename)
            try:
                widgets = extract_widgets_from_dashboard(file_path)
                for widget in widgets:
                    widget_mappings[widget['widget_id']] = {
                        'type': widget['widget_type'],
                        'subtype': widget['widget_subtype']
                    }
                print(f"Processed {filename}: found {len(widgets)} widgets")
            except Exception as e:
                print(f"Error processing {filename}: {e}")
    
    # Read existing CSV and update types
    input_csv = '/Users/<USER>/projects/Wrok/econ/dashboard_metadata.csv'
    output_csv = '/Users/<USER>/projects/Wrok/econ/dashboard_metadata_updated.csv'
    
    with open(input_csv, 'r') as infile:
        reader = csv.DictReader(infile)
        rows = list(reader)
        fieldnames = [f for f in reader.fieldnames if f is not None]
        
    # Add widget_subtype if not present
    if 'widget_subtype' not in fieldnames:
        fieldnames.append('widget_subtype')
    
    with open(output_csv, 'w', newline='') as outfile:
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for row in rows:
            widget_id = row['widget_id']
            if widget_id in widget_mappings:
                # Update the widget type with actual Sisense type
                row['widget_type'] = widget_mappings[widget_id]['type']
                row['widget_subtype'] = widget_mappings[widget_id]['subtype']
            else:
                row['widget_subtype'] = ''
            
            writer.writerow(row)
    
    print(f"\nUpdated CSV saved to: {output_csv}")
    print(f"Total widgets mapped: {len(widget_mappings)}")

if __name__ == "__main__":
    main()